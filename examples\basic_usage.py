"""
大华视频云SDK基础使用示例

演示如何使用SDK进行登录、获取组织树、设备信息等基本操作。
"""

import sys
import os
import time
import threading

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from dahua_sdk.login.login import Login
from dahua_sdk.login.keep_login import KeepLogin
from dahua_sdk.login.login_out import LoginOut
from dahua_sdk.basic.org_tree import OrgTree
from dahua_sdk.basic.dev_info import DevInfo
from dahua_sdk.util.base_user_info import BaseUserInfo


class DahuaSDKDemo:
    """大华SDK演示类"""
    
    def __init__(self):
        self.keep_alive_thread = None
        self.keep_alive_running = False
    
    def login(self):
        """登录并获取token"""
        print("=== 开始登录 ===")
        try:
            # 使用配置文件中的信息登录
            response = Login.login(
                BaseUserInfo.ip,
                BaseUserInfo.port,
                BaseUserInfo.username,
                BaseUserInfo.password
            )
            
            if response:
                import json
                response_map = json.loads(response)
                token = response_map.get("token")
                
                if token:
                    # 保存token
                    BaseUserInfo.save_token(token)
                    print(f"登录成功，token: {token[:20]}...")
                    
                    # 启动保活线程
                    self.start_keep_alive()
                    return True
                else:
                    message = response_map.get("message", "未知错误")
                    print(f"登录失败: {message}")
                    return False
            else:
                print("登录请求失败")
                return False
                
        except Exception as e:
            print(f"登录异常: {e}")
            return False
    
    def start_keep_alive(self):
        """启动保活线程"""
        print("启动保活线程...")
        self.keep_alive_running = True
        self.keep_alive_thread = threading.Thread(target=self._keep_alive_worker)
        self.keep_alive_thread.daemon = True
        self.keep_alive_thread.start()
    
    def _keep_alive_worker(self):
        """保活工作线程"""
        while self.keep_alive_running:
            try:
                time.sleep(110)  # 每110秒保活一次
                if self.keep_alive_running:
                    response = KeepLogin.keep_login(
                        BaseUserInfo.ip,
                        BaseUserInfo.port,
                        BaseUserInfo.token
                    )
                    if response:
                        print("保活成功")
                    else:
                        print("保活失败")
            except Exception as e:
                print(f"保活异常: {e}")
    
    def stop_keep_alive(self):
        """停止保活线程"""
        if self.keep_alive_running:
            print("停止保活线程...")
            self.keep_alive_running = False
            if self.keep_alive_thread:
                self.keep_alive_thread.join(timeout=5)
    
    def get_org_tree(self):
        """获取组织树"""
        print("\n=== 获取组织树 ===")
        try:
            response = OrgTree.get_org_tree(
                BaseUserInfo.ip,
                BaseUserInfo.port,
                BaseUserInfo.token
            )
            
            if response:
                import json
                response_map = json.loads(response)
                message = response_map.get("message")
                
                if message:
                    print(f"获取组织树失败: {message}")
                else:
                    print("组织树获取成功")
                    orgs = response_map.get("orgs", [])
                    print(f"共找到 {len(orgs)} 个组织")
                    
                    # 显示组织信息
                    for org in orgs[:3]:  # 只显示前3个
                        print(f"  组织ID: {org.get('orgId', 'N/A')}")
                        print(f"  组织名称: {org.get('orgName', 'N/A')}")
                        print(f"  父组织ID: {org.get('parentOrgId', 'N/A')}")
                        print("  ---")
                        
        except Exception as e:
            print(f"获取组织树异常: {e}")
    
    def get_device_info(self):
        """获取设备信息"""
        print("\n=== 获取设备信息 ===")
        try:
            response = DevInfo.get_all_devices(
                BaseUserInfo.ip,
                BaseUserInfo.port,
                BaseUserInfo.token
            )
            
            if response:
                import json
                response_map = json.loads(response)
                message = response_map.get("message")
                
                if message:
                    print(f"获取设备信息失败: {message}")
                else:
                    print("设备信息获取成功")
                    devices = response_map.get("devices", [])
                    print(f"共找到 {len(devices)} 个设备")
                    
                    # 显示设备信息
                    for device in devices[:3]:  # 只显示前3个
                        print(f"  设备ID: {device.get('deviceId', 'N/A')}")
                        print(f"  设备名称: {device.get('deviceName', 'N/A')}")
                        print(f"  设备类型: {device.get('deviceType', 'N/A')}")
                        print(f"  设备状态: {device.get('status', 'N/A')}")
                        print("  ---")
                        
        except Exception as e:
            print(f"获取设备信息异常: {e}")
    
    def logout(self):
        """登出"""
        print("\n=== 开始登出 ===")
        try:
            # 停止保活
            self.stop_keep_alive()
            
            # 登出
            response = LoginOut.login_out(
                BaseUserInfo.ip,
                BaseUserInfo.port,
                BaseUserInfo.token
            )
            
            if response:
                import json
                response_map = json.loads(response)
                message = response_map.get("message")
                
                if message:
                    print(f"登出失败: {message}")
                else:
                    print("登出成功")
                    BaseUserInfo.clear_token()
            else:
                print("登出请求失败")
                
        except Exception as e:
            print(f"登出异常: {e}")
    
    def run_demo(self):
        """运行完整演示"""
        print("大华视频云SDK演示程序")
        print("=" * 50)
        
        try:
            # 1. 登录
            if not self.login():
                print("登录失败，演示结束")
                return
            
            # 等待一下确保登录完成
            time.sleep(2)
            
            # 2. 获取组织树
            self.get_org_tree()
            
            # 3. 获取设备信息
            self.get_device_info()
            
            # 4. 等待一段时间演示保活
            print("\n=== 等待保活演示（30秒）===")
            time.sleep(30)
            
        except KeyboardInterrupt:
            print("\n用户中断演示")
        except Exception as e:
            print(f"演示过程中发生异常: {e}")
        finally:
            # 5. 登出
            self.logout()
            print("\n演示结束")


def main():
    """主函数"""
    demo = DahuaSDKDemo()
    demo.run_demo()


if __name__ == "__main__":
    main()
