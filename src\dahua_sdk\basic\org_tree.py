"""
组织树接口

获取组织架构树形结构。
"""

import json
from typing import Optional
from ..util.base_user_info import BaseUserInfo
from ..util.http_enum import HttpEnum
from ..util.http_test_utils import HttpTestUtils


class OrgTree(BaseUserInfo):
    """组织树接口类"""

    ACTION = "/videoService/devicesManager/deviceTree"

    @staticmethod
    def get_org_tree(ip: str, port: int, token: str) -> Optional[str]:
        """
        获取组织树

        Args:
            ip: 服务器IP
            port: 服务器端口
            token: 认证token

        Returns:
            组织树JSON字符串
        """
        response = HttpTestUtils.http_request(
            HttpEnum.GET, ip, port, OrgTree.ACTION, token, ""
        )
        return response

    @staticmethod
    def main():
        """
        主方法，用于测试组织树功能

        获取组织树接口，调用该接口前需要先登录获取token
        """
        try:
            # 检查是否已登录
            if not BaseUserInfo.token:
                print("请先登录获取token")
                return

            # 获取组织树
            response = OrgTree.get_org_tree(
                BaseUserInfo.ip, BaseUserInfo.port, BaseUserInfo.token
            )

            if response:
                try:
                    response_map = json.loads(response)
                    message = response_map.get("message")

                    if message:
                        print(f"获取组织树失败: {message}")
                    else:
                        print("获取组织树成功:")
                        print(json.dumps(response_map, indent=2, ensure_ascii=False))

                except json.JSONDecodeError:
                    print("组织树响应JSON解析失败")
                    print(f"原始响应: {response}")
            else:
                print("获取组织树请求失败")

        except Exception as e:
            print(f"获取组织树异常: {e}")


if __name__ == "__main__":
    OrgTree.main()
