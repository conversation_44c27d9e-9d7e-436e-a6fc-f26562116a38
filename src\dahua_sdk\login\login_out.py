"""
登出接口

销毁登录会话。
"""

import json
from typing import Optional
from ..util.base_user_info import BaseUserInfo
from ..util.http_enum import HttpEnum
from ..util.http_test_utils import HttpTestUtils


class LoginOut(BaseUserInfo):
    """登出接口类"""
    
    ACTION = "/videoService/accounts/session"
    
    @staticmethod
    def login_out(ip: str, port: int, token: str) -> Optional[str]:
        """
        登出接口
        
        Args:
            ip: 服务器IP
            port: 服务器端口
            token: 认证token
            
        Returns:
            响应JSON字符串
        """
        response = HttpTestUtils.http_request(
            HttpEnum.DELETE, ip, port, LoginOut.ACTION, token, ""
        )
        return response
    
    @staticmethod
    def main():
        """
        主方法，用于测试登出功能
        
        登出接口，调用该接口后，token会失效
        """
        try:
            # 检查是否已登录
            if not BaseUserInfo.token:
                print("当前未登录")
                return
            
            # 调用登出接口
            response = LoginOut.login_out(
                BaseUserInfo.ip,
                BaseUserInfo.port,
                BaseUserInfo.token
            )
            
            if response:
                try:
                    response_map = json.loads(response)
                    message = response_map.get("message")
                    
                    if message:
                        print(f"登出失败: {message}")
                    else:
                        print("登出成功")
                        # 清除本地token
                        BaseUserInfo.clear_token()
                        
                except json.JSONDecodeError:
                    print("登出响应JSON解析失败")
            else:
                print("登出请求失败")
                
        except Exception as e:
            print(f"登出异常: {e}")


if __name__ == "__main__":
    LoginOut.main()
