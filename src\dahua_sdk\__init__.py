"""
大华视频云开放平台Python SDK

这是一个完整的大华视频云平台Python SDK演示程序，
提供了登录认证、基础视频功能、车辆智能、人像识别等功能模块。

主要功能模块：
- util: 核心工具类
- login: 登录认证模块  
- basic: 基础功能模块（组织管理、设备管理、视频功能、预置点、订阅等）
- vehicle: 车辆智能模块
- face_service: 人像识别模块

使用方法：
1. 配置连接信息（config/baseinfo.yaml）
2. 运行登录模块进行认证
3. 调用相应的功能模块

作者: 转换自大华视频云Java SDK
版本: 0.1.0
"""

__version__ = "0.1.0"
__author__ = "Dahua Video Cloud SDK"

# 导入主要模块
from .util import BaseUserInfo, HttpTestUtils, HttpEnum
from .login import Login, KeepLogin, LoginOut

__all__ = [
    "BaseUserInfo",
    "HttpTestUtils", 
    "HttpEnum",
    "Login",
    "KeepLogin",
    "LoginOut",
]
